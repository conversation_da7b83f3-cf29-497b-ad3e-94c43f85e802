import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { eq, and, asc } from "drizzle-orm";
import { respData, respErr } from "~/lib/api/resp";
import { createDb } from "~/lib/db/db";
import { conversations, messages } from "~/lib/db/schema";
import { requireUnifiedAuth } from "~/lib/auth/unified-middleware.server";

// GET: Get a specific conversation with its messages
export async function loader({ request, params, context }: LoaderFunctionArgs) {
  try {
    const { conversationId } = params;

    if (!conversationId) {
      return respErr("Conversation ID is required");
    }

    const user = await requireUnifiedAuth(request, context.cloudflare?.env);

    const db = createDb(context.cloudflare!.env.DATABASE_URL);

    // First, verify the conversation belongs to the user
    const conversation = await db
      .select()
      .from(conversations)
      .where(and(eq(conversations.id, conversationId), eq(conversations.userUuid, user.id)))
      .limit(1);

    if (conversation.length === 0) {
      return respErr("Conversation not found");
    }

    // Get all messages for this conversation
    const conversationMessages = await db
      .select({
        id: messages.id,
        role: messages.role,
        content: messages.content,
        model: messages.model,
        provider: messages.provider,
        tokenCount: messages.tokenCount,
        metadata: messages.metadata,
        createdAt: messages.createdAt,
      })
      .from(messages)
      .where(eq(messages.conversationId, conversationId))
      .orderBy(asc(messages.createdAt));

    return respData({
      conversation: conversation[0],
      messages: conversationMessages,
    });
  } catch (error) {
    console.error("Failed to fetch conversation:", error);
    return respErr("Failed to fetch conversation");
  }
}
