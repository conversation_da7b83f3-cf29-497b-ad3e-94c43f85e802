/**
 * Unified Authentication Middleware
 * Supports both Google One Tap and Neon Auth based on configuration
 */

import { redirect } from "@remix-run/cloudflare";
import { getAuthConfig } from "./auth-config";
import {
  getUser as getGoogleOneTapUser,
  type AuthenticatedUser,
  type AuthResult,
} from "./middleware.server";
import { getNeonAuthUser, isNeonAuthRequest } from "./neon-auth.server";

/**
 * Get current user using the configured authentication provider
 */
export async function getUnifiedUser(
  request: Request,
  env?: Record<string, string | undefined>
): Promise<AuthResult> {
  try {
    const authConfig = getAuthConfig(env);

    // If Neon Auth is enabled, try it first
    if (authConfig.neonAuth.enabled) {
      // Check if this request is specifically using Neon Auth
      if (isNeonAuthRequest(request, env)) {
        const neonResult = await getNeonAuthUser(request, env);

        if (neonResult.success && neonResult.user) {
          return {
            success: true,
            user: neonResult.user,
          };
        } else {
          return {
            success: false,
            error: neonResult.error || "Neon Auth authentication failed",
            redirectTo: "/auth/neon-login",
          };
        }
      }
    }

    // Fall back to Google One Tap authentication
    if (authConfig.googleOneTap.enabled) {
      return await getGoogleOneTapUser(request, env);
    }

    // No authentication provider is enabled
    return {
      success: false,
      error: "No authentication provider is configured",
      redirectTo: "/auth/login",
    };
  } catch (error) {
    console.error("Unified auth error:", error);
    return {
      success: false,
      error: "Authentication failed",
      redirectTo: "/auth/login",
    };
  }
}

/**
 * Require authentication using the configured provider
 */
export async function requireUnifiedAuth(
  request: Request,
  env?: Record<string, string | undefined>
): Promise<AuthenticatedUser> {
  const result = await getUnifiedUser(request, env);

  if (!result.success) {
    const authConfig = getAuthConfig(env);
    const redirectTo = authConfig.neonAuth.enabled ? "/auth/neon-login" : "/auth/login";
    throw redirect(result.redirectTo || redirectTo);
  }

  return result.user;
}

/**
 * Optional authentication using the configured provider
 */
export async function optionalUnifiedAuth(
  request: Request,
  env?: Record<string, string | undefined>
): Promise<AuthenticatedUser | null> {
  const result = await getUnifiedUser(request, env);
  return result.success ? result.user : null;
}

/**
 * Get the appropriate login URL based on configuration
 */
export function getLoginUrl(env?: Record<string, string | undefined>): string {
  const authConfig = getAuthConfig(env);

  if (authConfig.neonAuth.enabled) {
    return "/auth/neon-login";
  }

  return "/auth/login";
}

/**
 * Get the appropriate logout URL based on configuration
 */
export function getLogoutUrl(env?: Record<string, string | undefined>): string {
  const authConfig = getAuthConfig(env);

  if (authConfig.neonAuth.enabled) {
    return "/neon-auth/sign-out";
  }

  return "/auth/logout";
}
