/**
 * Neon Auth Provider for Remix
 * Experimental wrapper to make Neon Auth work with Remix routing
 */

import { StackProvider, StackTheme } from "@stackframe/react";
import { useLocation } from "@remix-run/react";
import { type ReactNode, useEffect, useState } from "react";
import { neonAuthClient, isNeonAuthEnabled } from "~/lib/auth/neon-auth.client";

interface NeonAuthProviderProps {
  children: ReactNode;
  authConfig?: {
    projectId?: string;
    publishableKey?: string;
    enabled?: boolean;
  };
}

export function NeonAuthProvider({ children, authConfig }: NeonAuthProviderProps) {
  const [isEnabled, setIsEnabled] = useState(false);
  const [client, setClient] = useState(neonAuthClient);

  useEffect(() => {
    // Check if Neon Auth should be enabled
    const enabled = authConfig?.enabled ?? isNeonAuthEnabled();
    setIsEnabled(enabled);

    // Initialize client if not already done and if enabled
    if (enabled && !client && authConfig?.projectId && authConfig?.publishableKey) {
      // Use a simpler approach without dynamic imports for now
      try {
        const { createNeonAuthClient } = require("~/lib/auth/neon-auth.client");
        const newClient = createNeonAuthClient({
          projectId: authConfig.projectId!,
          publishableClientKey: authConfig.publishableKey!,
        });
        setClient(newClient);
      } catch (error) {
        console.error("Failed to initialize Neon Auth client:", error);
      }
    }
  }, [authConfig, client]);

  // If Neon Auth is not enabled, just render children
  if (!isEnabled || !client) {
    return <>{children}</>;
  }

  return (
    <StackProvider app={client}>
      <StackTheme>{children}</StackTheme>
    </StackProvider>
  );
}

/**
 * Neon Auth Handler Component for Remix Routes
 * This handles the authentication flow routes
 */
interface NeonAuthHandlerProps {
  fullPage?: boolean;
}

export function NeonAuthHandler({ fullPage = true }: NeonAuthHandlerProps) {
  const location = useLocation();
  const [client, setClient] = useState(neonAuthClient);

  useEffect(() => {
    if (!client) {
      try {
        const { neonAuthClient: importedClient } = require("~/lib/auth/neon-auth.client");
        setClient(importedClient);
      } catch (error) {
        console.error("Failed to load Neon Auth client:", error);
      }
    }
  }, [client]);

  if (!client) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading authentication...</p>
        </div>
      </div>
    );
  }

  // Import StackHandler to avoid SSR issues
  const [StackHandler, setStackHandler] = useState<any>(null);

  useEffect(() => {
    try {
      const { StackHandler: ImportedStackHandler } = require("@stackframe/react");
      setStackHandler(() => ImportedStackHandler);
    } catch (error) {
      console.error("Failed to load StackHandler:", error);
    }
  }, []);

  if (!StackHandler) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading authentication handler...</p>
        </div>
      </div>
    );
  }

  return <StackHandler app={client} location={location.pathname} fullPage={fullPage} />;
}

/**
 * Hook to check if Neon Auth is available and configured
 */
export function useNeonAuthStatus() {
  const [status, setStatus] = useState({
    enabled: false,
    configured: false,
    client: null as any,
  });

  useEffect(() => {
    const enabled = isNeonAuthEnabled();
    const client = neonAuthClient;

    setStatus({
      enabled,
      configured: enabled && client !== null,
      client,
    });
  }, []);

  return status;
}
